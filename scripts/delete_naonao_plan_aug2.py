#!/usr/bin/env python3
"""
删除 naonao 用户 8 月 2 日的学习计划
"""

import sys
import os
from datetime import date

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from src.models.user import User
from src.models.planning import LearningPlan
from src.core.database import db


def delete_naonao_plan_aug2():
    """删除 naonao 用户 8 月 2 日的学习计划"""
    
    print("=" * 60)
    print("删除 naonao 用户 8 月 2 日的学习计划")
    print("=" * 60)
    
    try:
        # 1. 获取 naonao 用户信息
        user = User.get_by_username('naonao')
        if not user:
            print("❌ 用户 'naonao' 不存在")
            return False
        
        user_id = user['id']
        print(f"✅ 找到用户: naonao (ID: {user_id})")
        
        # 2. 设置目标日期 (2025年8月2日)
        target_date = date(2025, 8, 2)
        print(f"📅 目标日期: {target_date}")
        
        # 3. 检查该日期是否有学习计划
        existing_plans = LearningPlan.get_daily_plan(user_id, target_date)
        if not existing_plans:
            print(f"ℹ️  用户 naonao 在 {target_date} 没有学习计划")
            return True
        
        print(f"📋 找到 {len(existing_plans)} 条学习计划:")
        for plan in existing_plans:
            print(f"   - ID: {plan['id']}, 单词: {plan['english_word']}, 类型: {plan['item_type']}, 星级: {plan['star_level']}")
        
        # 4. 确认删除
        print(f"\n⚠️  即将删除 naonao 用户在 {target_date} 的所有学习计划")
        confirm = input("确认删除吗？(y/N): ").strip().lower()
        
        if confirm != 'y':
            print("❌ 操作已取消")
            return False
        
        # 5. 执行删除
        deleted_count = LearningPlan.clear_date_plan(user_id, target_date)
        
        if deleted_count > 0:
            print(f"✅ 成功删除 {deleted_count} 条学习计划")
            
            # 6. 验证删除结果
            remaining_plans = LearningPlan.get_daily_plan(user_id, target_date)
            if not remaining_plans:
                print("✅ 验证成功: 该日期已无学习计划")
            else:
                print(f"⚠️  警告: 仍有 {len(remaining_plans)} 条计划未删除")
                
        else:
            print("❌ 删除失败: 没有记录被删除")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 删除过程中发生错误: {e}")
        return False


def show_user_plans_summary(username: str = 'naonao', days_range: int = 7):
    """显示用户最近几天的学习计划概览"""
    
    print(f"\n📊 {username} 用户最近 {days_range} 天的学习计划概览:")
    print("-" * 60)
    
    try:
        user = User.get_by_username(username)
        if not user:
            print(f"❌ 用户 '{username}' 不存在")
            return
        
        user_id = user['id']
        
        # 检查最近几天的计划
        from datetime import timedelta
        today = date.today()
        
        for i in range(days_range):
            check_date = today + timedelta(days=i-3)  # 前3天到后3天
            plans = LearningPlan.get_daily_plan(user_id, check_date)
            
            status = "✅" if plans else "❌"
            count = len(plans) if plans else 0
            
            print(f"{status} {check_date}: {count} 条计划")
            
    except Exception as e:
        print(f"❌ 查询过程中发生错误: {e}")


if __name__ == "__main__":
    print("🚀 开始执行删除操作")
    
    # 1. 显示用户计划概览
    show_user_plans_summary('naonao')
    
    # 2. 执行删除操作
    success = delete_naonao_plan_aug2()
    
    if success:
        print("\n🎉 删除操作完成!")
        # 3. 再次显示概览确认结果
        show_user_plans_summary('naonao')
    else:
        print("\n💥 删除操作失败!")
    
    print("\n✅ 脚本执行完成")
